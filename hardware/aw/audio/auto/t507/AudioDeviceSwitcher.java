/*
 * AudioDeviceSwitcher.java
 * Android Application Layer Interface for T507 Audio Device Switching
 */

package com.allwinner.audio;

import android.content.Context;
import android.media.AudioManager;
import android.os.SystemProperties;
import android.util.Log;

/**
 * Audio Device Switcher for T507 Car Audio System
 * Provides easy switching between Bluetooth headset and car speakers/microphone
 */
public class AudioDeviceSwitcher {
    private static final String TAG = "AudioDeviceSwitcher";
    
    // Audio mode constants
    public static final int MODE_BLUETOOTH = 0;
    public static final int MODE_SPEAKER_MIC = 1;
    
    // Property keys for audio mode switching
    private static final String PROP_AUDIO_MODE_SWITCH = "vendor.audio.mode.switch";
    private static final String PROP_AUDIO_CURRENT_MODE = "vendor.audio.current.mode";
    
    private AudioManager mAudioManager;
    private Context mContext;
    
    public AudioDeviceSwitcher(Context context) {
        mContext = context;
        mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
    }
    
    /**
     * Switch to Bluetooth audio mode
     * Both input and output will use Bluetooth headset
     */
    public boolean switchToBluetoothMode() {
        Log.d(TAG, "Switching to Bluetooth audio mode");
        
        try {
            // Method 1: Use AudioManager routing
            mAudioManager.setParameters("routing=" + AudioManager.DEVICE_OUT_BLUETOOTH_SCO_HEADSET);
            
            // Method 2: Use system property
            SystemProperties.set(PROP_AUDIO_MODE_SWITCH, String.valueOf(MODE_BLUETOOTH));
            
            // Method 3: Direct HAL parameter
            mAudioManager.setParameters("audio_mode_switch=" + MODE_BLUETOOTH);
            
            // Update current mode property
            SystemProperties.set(PROP_AUDIO_CURRENT_MODE, String.valueOf(MODE_BLUETOOTH));
            
            Log.i(TAG, "Successfully switched to Bluetooth audio mode");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to switch to Bluetooth mode", e);
            return false;
        }
    }
    
    /**
     * Switch to Speaker/Microphone audio mode
     * Output will use car speakers, input will use car microphone
     */
    public boolean switchToSpeakerMode() {
        Log.d(TAG, "Switching to Speaker/Microphone audio mode");
        
        try {
            // Method 1: Use AudioManager routing
            mAudioManager.setParameters("routing=" + AudioManager.DEVICE_OUT_SPEAKER);
            
            // Method 2: Use system property
            SystemProperties.set(PROP_AUDIO_MODE_SWITCH, String.valueOf(MODE_SPEAKER_MIC));
            
            // Method 3: Direct HAL parameter
            mAudioManager.setParameters("audio_mode_switch=" + MODE_SPEAKER_MIC);
            
            // Update current mode property
            SystemProperties.set(PROP_AUDIO_CURRENT_MODE, String.valueOf(MODE_SPEAKER_MIC));
            
            Log.i(TAG, "Successfully switched to Speaker/Microphone audio mode");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to switch to Speaker mode", e);
            return false;
        }
    }
    
    /**
     * Get current audio mode
     * @return MODE_BLUETOOTH or MODE_SPEAKER_MIC
     */
    public int getCurrentMode() {
        String currentMode = SystemProperties.get(PROP_AUDIO_CURRENT_MODE, "1");
        return Integer.parseInt(currentMode);
    }
    
    /**
     * Check if Bluetooth audio mode is active
     */
    public boolean isBluetoothModeActive() {
        return getCurrentMode() == MODE_BLUETOOTH;
    }
    
    /**
     * Check if Speaker/Mic audio mode is active
     */
    public boolean isSpeakerModeActive() {
        return getCurrentMode() == MODE_SPEAKER_MIC;
    }
    
    /**
     * Toggle between Bluetooth and Speaker modes
     */
    public boolean toggleAudioMode() {
        if (isBluetoothModeActive()) {
            return switchToSpeakerMode();
        } else {
            return switchToBluetoothMode();
        }
    }
    
    /**
     * Get current audio mode as string
     */
    public String getCurrentModeString() {
        switch (getCurrentMode()) {
            case MODE_BLUETOOTH:
                return "Bluetooth";
            case MODE_SPEAKER_MIC:
                return "Speaker/Microphone";
            default:
                return "Unknown";
        }
    }
    
    /**
     * Test audio device switching
     * Useful for debugging and verification
     */
    public void testAudioSwitching() {
        Log.d(TAG, "Starting audio device switching test");
        
        // Test switching to speaker mode
        Log.d(TAG, "Testing Speaker mode...");
        switchToSpeakerMode();
        
        try {
            Thread.sleep(3000); // Wait 3 seconds
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Test switching to Bluetooth mode
        Log.d(TAG, "Testing Bluetooth mode...");
        switchToBluetoothMode();
        
        try {
            Thread.sleep(3000); // Wait 3 seconds
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Switch back to speaker mode
        Log.d(TAG, "Switching back to Speaker mode...");
        switchToSpeakerMode();
        
        Log.d(TAG, "Audio device switching test completed");
    }
    
    /**
     * Get audio system status for debugging
     */
    public String getAudioSystemStatus() {
        StringBuilder status = new StringBuilder();
        
        status.append("Current Audio Mode: ").append(getCurrentModeString()).append("\n");
        status.append("Audio Mode Property: ").append(SystemProperties.get(PROP_AUDIO_CURRENT_MODE, "unknown")).append("\n");
        
        // Get AudioManager parameters
        try {
            String routing = mAudioManager.getParameters("routing");
            status.append("Current Routing: ").append(routing).append("\n");
        } catch (Exception e) {
            status.append("Current Routing: Error getting routing info\n");
        }
        
        return status.toString();
    }
}

/**
 * Example usage in an Activity:
 * 
 * AudioDeviceSwitcher switcher = new AudioDeviceSwitcher(this);
 * 
 * // Switch to Bluetooth
 * switcher.switchToBluetoothMode();
 * 
 * // Switch to Speaker
 * switcher.switchToSpeakerMode();
 * 
 * // Toggle between modes
 * switcher.toggleAudioMode();
 * 
 * // Check current mode
 * if (switcher.isBluetoothModeActive()) {
 *     Log.d(TAG, "Currently using Bluetooth audio");
 * }
 */
