# T507 Android 10 车机音频设备切换解决方案

## 概述

本解决方案为T507 Android 10车机系统提供了完整的音频设备切换功能，支持在蓝牙耳机和车载喇叭/麦克风之间自由切换。

## 系统架构

### 音频设备映射
- **蓝牙设备**: `sndbt936b` (card 4) - BT936B芯片
- **车载音频**: `sndes721010043` (card 3) - TYW音频设备  
- **内置编解码器**: `audiocodec` (card 0) - 车载喇叭输出
- **音频路由**: `sndahub` (card 1) - AHUB音频路由器

### 音频模式
1. **蓝牙模式**: 使用BT936B进行音频输入和输出
2. **车载模式**: 使用CODEC输出到喇叭，TYW输入从麦克风

## 实现方案

### 1. HAL层增强 (audio_hw.c)

#### 新增功能
- 增强的设备切换函数 `switch_audio_mode()`
- 改进的输出设备参数处理
- 改进的输入设备参数处理
- 新增参数接口 `audio_mode_switch`

#### 关键函数
```c
// 音频模式切换主函数
static int switch_audio_mode(struct sunxi_audio_device *adev, int mode);

// 模式定义
#define MODE_BLUETOOTH    0  // 蓝牙模式
#define MODE_SPEAKER_MIC  1  // 喇叭/麦克风模式
```

### 2. 应用层接口 (AudioDeviceSwitcher.java)

#### 主要方法
```java
// 切换到蓝牙模式
public boolean switchToBluetoothMode()

// 切换到喇叭模式  
public boolean switchToSpeakerMode()

// 切换模式
public boolean toggleAudioMode()

// 获取当前模式
public int getCurrentMode()
```

### 3. 命令行工具 (audio_switch.sh)

#### 使用方法
```bash
# 切换到蓝牙模式
./audio_switch.sh bluetooth

# 切换到喇叭模式
./audio_switch.sh speaker

# 查看状态
./audio_switch.sh status

# 运行测试
./audio_switch.sh test
```

### 4. 测试工具 (audio_switch_test.c)

编译后的可执行文件用于验证音频切换功能。

## 使用方法

### 方法1: 通过Android应用
```java
AudioDeviceSwitcher switcher = new AudioDeviceSwitcher(context);

// 切换到蓝牙
switcher.switchToBluetoothMode();

// 切换到喇叭
switcher.switchToSpeakerMode();
```

### 方法2: 通过系统属性
```bash
# 切换到蓝牙模式
setprop vendor.audio.mode.switch 0

# 切换到喇叭模式
setprop vendor.audio.mode.switch 1
```

### 方法3: 通过AudioManager参数
```java
AudioManager am = (AudioManager) getSystemService(Context.AUDIO_SERVICE);

// 切换到蓝牙模式
am.setParameters("audio_mode_switch=0");

// 切换到喇叭模式
am.setParameters("audio_mode_switch=1");
```

### 方法4: 通过Shell脚本
```bash
# 使用提供的脚本
./audio_switch.sh bluetooth  # 蓝牙模式
./audio_switch.sh speaker    # 喇叭模式
```

## 编译和部署

### 1. 编译HAL
```bash
cd hardware/aw/audio/auto/t507
mm -j8
```

### 2. 编译测试工具
```bash
# 测试工具会随HAL一起编译
# 生成的文件: audio_switch_test
```

### 3. 部署脚本
```bash
# 将脚本推送到设备
adb push audio_switch.sh /system/bin/
adb shell chmod 755 /system/bin/audio_switch.sh
```

## 测试验证

### 1. 基本功能测试
```bash
# 运行自动测试
./audio_switch.sh test

# 或使用测试工具
audio_switch_test 4  # 运行所有测试
```

### 2. 手动验证
```bash
# 检查当前状态
./audio_switch.sh status

# 切换到蓝牙并播放音频
./audio_switch.sh bluetooth
tinyplay_ahub /system/media/audio/ringtones/Ring_Synth_04.ogg

# 切换到喇叭并播放音频
./audio_switch.sh speaker  
tinyplay /system/media/audio/ringtones/Ring_Synth_04.ogg
```

### 3. 录音测试
```bash
# 蓝牙录音测试
./audio_switch.sh bluetooth
tinycap_ahub /sdcard/bt_record.wav

# 麦克风录音测试
./audio_switch.sh speaker
tinycap /sdcard/mic_record.wav
```

## 故障排除

### 常见问题

1. **音频切换无效**
   - 检查音频设备是否存在: `cat /proc/asound/cards`
   - 检查AHUB状态: `tinymix -D 1`

2. **蓝牙音频无声音**
   - 确认BT936B设备已连接
   - 检查I2S0路由配置

3. **喇叭无声音**
   - 检查CODEC设备状态
   - 确认音量设置

### 调试命令
```bash
# 查看音频设备
cat /proc/asound/cards

# 查看AHUB混音器状态
tinymix -D 1

# 查看系统属性
getprop | grep audio

# 查看日志
logcat | grep -E "(AudioSwitch|audio_hw)"
```

## 技术细节

### AHUB路由配置
- **I2S0**: 蓝牙音频 (BT936B)
- **I2S2**: TYW音频设备
- **CODEC**: 车载喇叭输出

### 音频路径
1. **蓝牙模式**: APBIF0 ↔ I2S0 ↔ BT936B
2. **喇叭模式**: APBIF2 ↔ I2S2 ↔ TYW + CODEC

## 注意事项

1. 切换音频模式时会自动停止当前的音频流
2. 建议在切换前保存应用状态
3. 蓝牙设备需要先配对连接
4. 某些应用可能需要重启才能识别新的音频设备

## 版本信息

- **版本**: 1.0
- **适用平台**: T507 Android 10
- **测试状态**: 已验证基本功能
- **维护者**: 音频团队
