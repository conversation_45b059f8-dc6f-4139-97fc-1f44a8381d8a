# T507 简单音频设备切换解决方案

## 概述

这是一个简化的音频设备切换解决方案，只需要最少的代码修改就能实现蓝牙耳机和车载喇叭/麦克风之间的自动切换。

## 核心原理

实现真正的动态音频路由切换：

1. **参数触发切换** - `out_set_parameters` 和 `in_set_parameters` 检测到设备变化时立即触发切换
2. **强制流重启** - 调用 `do_output_standby()` 和 `do_input_standby()` 关闭当前PCM设备
3. **重新选择设备** - 调用 `select_output_device()` 和 `select_input_device()` 选择新的音频卡
4. **自动重新打开** - 下次音频读写时，流会用新设备重新打开
5. **无缝切换** - 实现播放过程中的动态路由切换

## 代码修改

### 1. 实现输出设备选择函数

```c
static void select_output_device(struct sunxi_audio_device *adev)
{
    ALOGD("select_output_device: mode=%d", adev->mode);

    // Check if we have an active output stream and its device type
    if (adev->active_output && adev->active_output->device) {
        int device = adev->active_output->device;
        ALOGD("select_output_device: device=0x%x", device);

        // Select appropriate output cards based on device type
        if (device == AUDIO_DEVICE_OUT_BLUETOOTH_SCO ||
            device == AUDIO_DEVICE_OUT_BLUETOOTH_SCO_HEADSET ||
            device == AUDIO_DEVICE_OUT_BLUETOOTH_SCO_CARKIT) {

            ALOGD("Selecting Bluetooth output device");
            adev->output_active_cards = AUDIO_CARD_TYW_BT936B;

        } else if (device == AUDIO_DEVICE_OUT_SPEAKER) {
            ALOGD("Selecting Speaker output device");
            adev->output_active_cards = AUDIO_CARD_CODEC;
        }

        // Apply the selection
        set_audio_devices_active(adev, AUDIO_OUT);
    }
}
```

### 2. 实现输入设备选择函数

```c
static void select_input_device(struct sunxi_audio_device *adev)
{
    ALOGD("select_input_device: mode=%d", adev->mode);

    // Check if we have an active input stream and its device type
    if (adev->active_input && adev->active_input->device) {
        int device = adev->active_input->device;
        ALOGD("select_input_device: device=0x%x", device);

        // Select appropriate input cards based on device type
        if (device == AUDIO_DEVICE_IN_BLUETOOTH_SCO_HEADSET) {
            ALOGD("Selecting Bluetooth input device");
            adev->input_active_cards = AUDIO_CARD_TYW_BT936B;

        } else if (device == AUDIO_DEVICE_IN_BUILTIN_MIC) {
            ALOGD("Selecting Built-in Mic input device");
            adev->input_active_cards = AUDIO_CARD_TYW;
        }

        // Apply the selection
        set_audio_devices_active(adev, AUDIO_IN);
    }
}
```

### 3. 动态切换的参数处理

```c
// 在 out_set_parameters 中实现动态切换
// Dynamic device switching: force stream to restart with new device
struct sunxi_audio_device *adev = out->dev;
pthread_mutex_lock(&adev->lock);

// Force current output stream to standby to close current PCM
if (adev->active_output) {
    do_output_standby(adev->active_output);
}

// Update the active output reference and call device selection
adev->active_output = out;
select_output_device(adev);

pthread_mutex_unlock(&adev->lock);
```

这样实现了真正的动态切换：当参数变化时立即关闭当前PCM设备，重新选择音频卡，下次音频操作时会用新设备重新打开。

## 设备映射

- **蓝牙模式**: `AUDIO_CARD_TYW_BT936B` (sndbt936b, card 4)
- **车载模式**: 
  - 输出: `AUDIO_CARD_CODEC` (audiocodec, card 0)
  - 输入: `AUDIO_CARD_TYW` (sndes721010043, card 3)

## 使用方法

### 自动切换
当用户在Android系统中：
- 连接蓝牙耳机 → 自动切换到蓝牙模式
- 断开蓝牙耳机 → 自动切换回车载模式
- 进行蓝牙通话 → 自动使用蓝牙输入输出
- 使用车载通话 → 自动使用车载麦克风和喇叭

### 手动测试
```bash
# 使用测试脚本
./simple_audio_test.sh manual_bt    # 手动切换到蓝牙
./simple_audio_test.sh manual_spk   # 手动切换到车载
./simple_audio_test.sh dynamic      # 测试播放过程中的动态切换
./simple_audio_test.sh status       # 查看当前状态
```

### 直接使用tinymix
```bash
# 切换到蓝牙模式
tinymix -D 1 "I2S0IN Switch" 1
tinymix -D 1 "I2S0OUT Switch" 1
tinymix -D 1 "I2S2IN Switch" 0
tinymix -D 1 "I2S2OUT Switch" 0

# 切换到车载模式
tinymix -D 1 "I2S0IN Switch" 0
tinymix -D 1 "I2S0OUT Switch" 0
tinymix -D 1 "I2S2IN Switch" 1
tinymix -D 1 "I2S2OUT Switch" 1
```

## 编译部署

```bash
# 编译HAL
cd hardware/aw/audio/auto/t507
mm -j8

# 重启AudioFlinger使新HAL生效
adb shell stop audioserver
adb shell start audioserver

# 测试功能
adb push simple_audio_test.sh /data/local/tmp/
adb shell chmod 755 /data/local/tmp/simple_audio_test.sh
adb shell /data/local/tmp/simple_audio_test.sh test
```

## 验证方法

1. **检查音频卡**
   ```bash
   cat /proc/asound/cards
   ```

2. **检查AHUB状态**
   ```bash
   tinymix -D 1 | grep "I2S.*Switch"
   ```

3. **测试蓝牙切换**
   - 连接蓝牙耳机
   - 播放音频，应该从蓝牙输出
   - 检查日志: `logcat | grep audio_hw`

4. **测试车载切换**
   - 断开蓝牙耳机
   - 播放音频，应该从车载喇叭输出

## 优势

1. **代码简洁** - 只添加了一个核心函数和几行调用代码
2. **自动工作** - 利用Android现有的设备切换机制
3. **无需应用修改** - 应用层无需任何改动
4. **可靠性高** - 基于现有的HAL框架，不破坏原有逻辑
5. **易于维护** - 代码量少，逻辑清晰

## 故障排除

如果切换不工作：

1. 检查音频卡是否存在
2. 检查AHUB mixer是否可访问
3. 查看HAL日志确认设备切换被触发
4. 使用手动tinymix命令验证硬件路由

这个简化方案应该能满足您的基本需求，代码量最少且功能可靠。
