# T507 简单音频设备切换解决方案

## 概述

这是一个简化的音频设备切换解决方案，只需要最少的代码修改就能实现蓝牙耳机和车载喇叭/麦克风之间的自动切换。

## 核心原理

当Android系统请求切换音频设备时（如连接蓝牙耳机），HAL会：

1. **捕获设备切换请求** - 通过 `out_set_parameters` 和 `in_set_parameters`
2. **判断设备类型** - 蓝牙设备 vs 车载设备
3. **设置活动卡片** - 更新 `output_active_cards` 和 `input_active_cards`
4. **强制流重启** - 调用 `force_all_standby()` 让所有流重新打开
5. **自动路由** - 流重新打开时会使用新的设备配置

## 代码修改

### 1. 新增设备切换函数 (audio_hw.c)

```c
// Simple audio mode switching based on device type
static void set_audio_mode_from_device(struct sunxi_audio_device *adev, int device, int is_output)
{
    ALOGD("set_audio_mode_from_device: device=0x%x, is_output=%d", device, is_output);
    
    // Check if it's a Bluetooth device
    if (device == AUDIO_DEVICE_OUT_BLUETOOTH_SCO ||
        device == AUDIO_DEVICE_OUT_BLUETOOTH_SCO_HEADSET ||
        device == AUDIO_DEVICE_OUT_BLUETOOTH_SCO_CARKIT ||
        device == AUDIO_DEVICE_IN_BLUETOOTH_SCO_HEADSET) {
        
        ALOGD("Switching to Bluetooth mode");
        adev->output_active_cards = AUDIO_CARD_TYW_BT936B;
        adev->input_active_cards = AUDIO_CARD_TYW_BT936B;
        
    } else if (device == AUDIO_DEVICE_OUT_SPEAKER ||
               device == AUDIO_DEVICE_IN_BUILTIN_MIC) {
        
        ALOGD("Switching to Speaker/Mic mode");
        adev->output_active_cards = AUDIO_CARD_CODEC;
        adev->input_active_cards = AUDIO_CARD_TYW;
    }
    
    // Force all streams to standby so they will reopen with new routing
    force_all_standby(adev);
    
    // Activate the new audio devices
    set_audio_devices_active(adev, AUDIO_OUT | AUDIO_IN);
}
```

### 2. 简化输出设备参数处理

```c
// 在 out_set_parameters 中替换复杂的切换逻辑为：
struct sunxi_audio_device *adev = out->dev;
pthread_mutex_lock(&adev->lock);
set_audio_mode_from_device(adev, val, 1);
pthread_mutex_unlock(&adev->lock);
```

### 3. 简化输入设备参数处理

```c
// 在 in_set_parameters 中替换复杂的切换逻辑为：
struct sunxi_audio_device *adev = in->dev;
pthread_mutex_lock(&adev->lock);
set_audio_mode_from_device(adev, val, 0);
pthread_mutex_unlock(&adev->lock);
```

## 设备映射

- **蓝牙模式**: `AUDIO_CARD_TYW_BT936B` (sndbt936b, card 4)
- **车载模式**: 
  - 输出: `AUDIO_CARD_CODEC` (audiocodec, card 0)
  - 输入: `AUDIO_CARD_TYW` (sndes721010043, card 3)

## 使用方法

### 自动切换
当用户在Android系统中：
- 连接蓝牙耳机 → 自动切换到蓝牙模式
- 断开蓝牙耳机 → 自动切换回车载模式
- 进行蓝牙通话 → 自动使用蓝牙输入输出
- 使用车载通话 → 自动使用车载麦克风和喇叭

### 手动测试
```bash
# 使用测试脚本
./simple_audio_test.sh manual_bt    # 手动切换到蓝牙
./simple_audio_test.sh manual_spk   # 手动切换到车载
./simple_audio_test.sh status       # 查看当前状态
```

### 直接使用tinymix
```bash
# 切换到蓝牙模式
tinymix -D 1 "I2S0IN Switch" 1
tinymix -D 1 "I2S0OUT Switch" 1
tinymix -D 1 "I2S2IN Switch" 0
tinymix -D 1 "I2S2OUT Switch" 0

# 切换到车载模式
tinymix -D 1 "I2S0IN Switch" 0
tinymix -D 1 "I2S0OUT Switch" 0
tinymix -D 1 "I2S2IN Switch" 1
tinymix -D 1 "I2S2OUT Switch" 1
```

## 编译部署

```bash
# 编译HAL
cd hardware/aw/audio/auto/t507
mm -j8

# 重启AudioFlinger使新HAL生效
adb shell stop audioserver
adb shell start audioserver

# 测试功能
adb push simple_audio_test.sh /data/local/tmp/
adb shell chmod 755 /data/local/tmp/simple_audio_test.sh
adb shell /data/local/tmp/simple_audio_test.sh test
```

## 验证方法

1. **检查音频卡**
   ```bash
   cat /proc/asound/cards
   ```

2. **检查AHUB状态**
   ```bash
   tinymix -D 1 | grep "I2S.*Switch"
   ```

3. **测试蓝牙切换**
   - 连接蓝牙耳机
   - 播放音频，应该从蓝牙输出
   - 检查日志: `logcat | grep audio_hw`

4. **测试车载切换**
   - 断开蓝牙耳机
   - 播放音频，应该从车载喇叭输出

## 优势

1. **代码简洁** - 只添加了一个核心函数和几行调用代码
2. **自动工作** - 利用Android现有的设备切换机制
3. **无需应用修改** - 应用层无需任何改动
4. **可靠性高** - 基于现有的HAL框架，不破坏原有逻辑
5. **易于维护** - 代码量少，逻辑清晰

## 故障排除

如果切换不工作：

1. 检查音频卡是否存在
2. 检查AHUB mixer是否可访问
3. 查看HAL日志确认设备切换被触发
4. 使用手动tinymix命令验证硬件路由

这个简化方案应该能满足您的基本需求，代码量最少且功能可靠。
