#!/system/bin/sh

# T507 Audio Device Switching Script
# Usage: audio_switch.sh [bluetooth|speaker|status|test]

LOG_TAG="AudioSwitch"

log_info() {
    echo "[$LOG_TAG] $1"
    log -p i -t $LOG_TAG "$1"
}

log_error() {
    echo "[$LOG_TAG] ERROR: $1"
    log -p e -t $LOG_TAG "ERROR: $1"
}

# Function to check current audio cards status
check_audio_status() {
    log_info "=== Current Audio System Status ==="
    
    echo "Available audio cards:"
    cat /proc/asound/cards
    
    echo ""
    echo "AHUB mixer controls:"
    tinymix -D 1 | grep -E "(I2S|Switch|Src)"
    
    echo ""
    echo "Current audio properties:"
    getprop | grep -E "(audio|bt|bluetooth)" | head -10
}

# Function to switch to Bluetooth mode
switch_to_bluetooth() {
    log_info "Switching to Bluetooth audio mode..."
    
    # Use setprop to trigger audio mode switch
    setprop vendor.audio.mode.switch 0
    
    # Alternative method using tinymix for AHUB
    log_info "Configuring AHUB for Bluetooth (I2S0)..."
    
    # Disable other I2S interfaces
    tinymix -D 1 "I2S1IN Switch" 0
    tinymix -D 1 "I2S1OUT Switch" 0
    tinymix -D 1 "I2S2IN Switch" 0
    tinymix -D 1 "I2S2OUT Switch" 0
    tinymix -D 1 "I2S3IN Switch" 0
    tinymix -D 1 "I2S3OUT Switch" 0
    
    # Enable I2S0 for Bluetooth
    tinymix -D 1 "I2S0IN Switch" 1
    tinymix -D 1 "I2S0OUT Switch" 1
    
    # Configure routing for Bluetooth
    tinymix -D 1 "APBIF0 Src Select" "I2S0_TXDIF"
    tinymix -D 1 "I2S0 Src Select" "APBIF_TXDIF0"
    
    log_info "Bluetooth audio mode activated"
    log_info "Audio will use BT936B (sndbt936b) for both input and output"
}

# Function to switch to Speaker/Mic mode
switch_to_speaker() {
    log_info "Switching to Speaker/Mic audio mode..."
    
    # Use setprop to trigger audio mode switch
    setprop vendor.audio.mode.switch 1
    
    # Alternative method using tinymix for AHUB
    log_info "Configuring AHUB for Speaker/Mic (I2S2)..."
    
    # Disable other I2S interfaces
    tinymix -D 1 "I2S0IN Switch" 0
    tinymix -D 1 "I2S0OUT Switch" 0
    tinymix -D 1 "I2S1IN Switch" 0
    tinymix -D 1 "I2S1OUT Switch" 0
    tinymix -D 1 "I2S3IN Switch" 0
    tinymix -D 1 "I2S3OUT Switch" 0
    
    # Enable I2S2 for Speaker/Mic
    tinymix -D 1 "I2S2IN Switch" 1
    tinymix -D 1 "I2S2OUT Switch" 1
    
    # Configure routing for Speaker/Mic
    tinymix -D 1 "APBIF2 Src Select" "I2S2_TXDIF"
    tinymix -D 1 "I2S2 Src Select" "APBIF_TXDIF2"
    
    log_info "Speaker/Mic audio mode activated"
    log_info "Audio will use CODEC for output and TYW (sndes721010043) for input"
}

# Function to run audio tests
run_tests() {
    log_info "Running audio device switching tests..."
    
    log_info "Test 1: Switch to Speaker mode"
    switch_to_speaker
    sleep 3
    
    log_info "Test 2: Switch to Bluetooth mode"
    switch_to_bluetooth
    sleep 3
    
    log_info "Test 3: Switch back to Speaker mode"
    switch_to_speaker
    sleep 2
    
    log_info "Audio switching tests completed"
}

# Function to show usage
show_usage() {
    echo "T507 Audio Device Switching Script"
    echo "=================================="
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  bluetooth  - Switch to Bluetooth audio mode (BT936B for I/O)"
    echo "  speaker    - Switch to Speaker/Mic mode (CODEC out, TYW in)"
    echo "  status     - Show current audio system status"
    echo "  test       - Run audio switching tests"
    echo "  help       - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 bluetooth   # Switch to Bluetooth headset"
    echo "  $0 speaker     # Switch to car speakers and mic"
    echo "  $0 status      # Check current audio configuration"
    echo ""
    echo "Audio Cards:"
    echo "  - audiocodec (card 0): Internal codec"
    echo "  - sndahub (card 1): Audio hub for routing"
    echo "  - sndspi10 (card 2): SPI audio"
    echo "  - sndes721010043 (card 3): TYW audio device"
    echo "  - sndbt936b (card 4): Bluetooth audio device"
}

# Main script logic
case "$1" in
    "bluetooth"|"bt")
        switch_to_bluetooth
        ;;
    "speaker"|"spk")
        switch_to_speaker
        ;;
    "status"|"stat")
        check_audio_status
        ;;
    "test")
        run_tests
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    "")
        log_error "No command specified"
        show_usage
        exit 1
        ;;
    *)
        log_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac

exit 0
