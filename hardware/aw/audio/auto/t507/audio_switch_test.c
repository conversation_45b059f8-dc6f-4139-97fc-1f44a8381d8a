/*
 * Audio Device Switching Test Tool
 * For T507 Android 10 Car Audio System
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <hardware/hardware.h>
#include <hardware/audio.h>
#include <system/audio.h>
#include <cutils/properties.h>

#define LOG_TAG "AudioSwitchTest"
#include <log/log.h>

// Test functions
static int test_bluetooth_mode(struct audio_hw_device *device)
{
    printf("=== Testing Bluetooth Audio Mode ===\n");
    
    // Set output device to Bluetooth SCO
    struct str_parms *parms = str_parms_create();
    str_parms_add_int(parms, AUDIO_PARAMETER_STREAM_ROUTING, AUDIO_DEVICE_OUT_BLUETOOTH_SCO_HEADSET);
    char *param_str = str_parms_to_str(parms);
    
    printf("Setting output to Bluetooth SCO...\n");
    int ret = device->set_parameters(device, param_str);
    if (ret != 0) {
        printf("ERROR: Failed to set Bluetooth output parameters\n");
        free(param_str);
        str_parms_destroy(parms);
        return -1;
    }
    
    free(param_str);
    str_parms_destroy(parms);
    
    // Set input device to Bluetooth SCO
    parms = str_parms_create();
    str_parms_add_int(parms, AUDIO_PARAMETER_STREAM_ROUTING, AUDIO_DEVICE_IN_BLUETOOTH_SCO_HEADSET);
    param_str = str_parms_to_str(parms);
    
    printf("Setting input to Bluetooth SCO...\n");
    ret = device->set_parameters(device, param_str);
    if (ret != 0) {
        printf("ERROR: Failed to set Bluetooth input parameters\n");
        free(param_str);
        str_parms_destroy(parms);
        return -1;
    }
    
    free(param_str);
    str_parms_destroy(parms);
    
    printf("SUCCESS: Bluetooth audio mode activated\n");
    return 0;
}

static int test_speaker_mode(struct audio_hw_device *device)
{
    printf("=== Testing Speaker/Mic Audio Mode ===\n");
    
    // Set output device to Speaker
    struct str_parms *parms = str_parms_create();
    str_parms_add_int(parms, AUDIO_PARAMETER_STREAM_ROUTING, AUDIO_DEVICE_OUT_SPEAKER);
    char *param_str = str_parms_to_str(parms);
    
    printf("Setting output to Speaker...\n");
    int ret = device->set_parameters(device, param_str);
    if (ret != 0) {
        printf("ERROR: Failed to set Speaker output parameters\n");
        free(param_str);
        str_parms_destroy(parms);
        return -1;
    }
    
    free(param_str);
    str_parms_destroy(parms);
    
    // Set input device to Built-in Mic
    parms = str_parms_create();
    str_parms_add_int(parms, AUDIO_PARAMETER_STREAM_ROUTING, AUDIO_DEVICE_IN_BUILTIN_MIC);
    param_str = str_parms_to_str(parms);
    
    printf("Setting input to Built-in Mic...\n");
    ret = device->set_parameters(device, param_str);
    if (ret != 0) {
        printf("ERROR: Failed to set Built-in Mic input parameters\n");
        free(param_str);
        str_parms_destroy(parms);
        return -1;
    }
    
    free(param_str);
    str_parms_destroy(parms);
    
    printf("SUCCESS: Speaker/Mic audio mode activated\n");
    return 0;
}

static int test_parameter_switch(struct audio_hw_device *device, int mode)
{
    printf("=== Testing Parameter-based Audio Mode Switch ===\n");
    
    struct str_parms *parms = str_parms_create();
    str_parms_add_int(parms, "audio_mode_switch", mode);
    char *param_str = str_parms_to_str(parms);
    
    printf("Switching to mode %d (0=Bluetooth, 1=Speaker)...\n", mode);
    int ret = device->set_parameters(device, param_str);
    if (ret != 0) {
        printf("ERROR: Failed to switch audio mode via parameter\n");
        free(param_str);
        str_parms_destroy(parms);
        return -1;
    }
    
    free(param_str);
    str_parms_destroy(parms);
    
    printf("SUCCESS: Audio mode switched via parameter\n");
    return 0;
}

int main(int argc, char *argv[])
{
    printf("T507 Audio Device Switching Test Tool\n");
    printf("=====================================\n");
    
    if (argc < 2) {
        printf("Usage: %s <test_mode>\n", argv[0]);
        printf("  0 - Test Bluetooth mode\n");
        printf("  1 - Test Speaker/Mic mode\n");
        printf("  2 - Test parameter switch to Bluetooth\n");
        printf("  3 - Test parameter switch to Speaker\n");
        printf("  4 - Run all tests\n");
        return 1;
    }
    
    int test_mode = atoi(argv[1]);
    
    // Load audio HAL
    const struct hw_module_t *module;
    int ret = hw_get_module(AUDIO_HARDWARE_MODULE_ID, &module);
    if (ret != 0) {
        printf("ERROR: Failed to load audio HAL module\n");
        return 1;
    }
    
    struct audio_hw_device *device;
    ret = audio_hw_device_open(module, &device);
    if (ret != 0) {
        printf("ERROR: Failed to open audio HAL device\n");
        return 1;
    }
    
    printf("Audio HAL device opened successfully\n\n");
    
    switch (test_mode) {
        case 0:
            ret = test_bluetooth_mode(device);
            break;
        case 1:
            ret = test_speaker_mode(device);
            break;
        case 2:
            ret = test_parameter_switch(device, 0);
            break;
        case 3:
            ret = test_parameter_switch(device, 1);
            break;
        case 4:
            printf("Running all tests...\n\n");
            ret = test_speaker_mode(device);
            if (ret == 0) {
                sleep(2);
                ret = test_bluetooth_mode(device);
            }
            if (ret == 0) {
                sleep(2);
                ret = test_parameter_switch(device, 1);
            }
            if (ret == 0) {
                sleep(2);
                ret = test_parameter_switch(device, 0);
            }
            break;
        default:
            printf("ERROR: Invalid test mode %d\n", test_mode);
            ret = -1;
            break;
    }
    
    // Close device
    audio_hw_device_close(device);
    
    if (ret == 0) {
        printf("\n=== ALL TESTS PASSED ===\n");
    } else {
        printf("\n=== TESTS FAILED ===\n");
    }
    
    return ret;
}
