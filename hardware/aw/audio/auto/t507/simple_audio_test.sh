#!/system/bin/sh

# Simple Audio Route Testing Script for T507
# Tests the basic audio device switching functionality

LOG_TAG="SimpleAudioTest"

log_info() {
    echo "[$LOG_TAG] $1"
    log -p i -t $LOG_TAG "$1"
}

# Function to test Bluetooth routing
test_bluetooth_route() {
    log_info "=== Testing Bluetooth Route ==="
    
    # Simulate Android system setting Bluetooth output device
    log_info "Setting Bluetooth SCO output device..."
    setprop vendor.audio.test.routing "4"  # AUDIO_DEVICE_OUT_BLUETOOTH_SCO_HEADSET
    
    # Check AHUB routing - should see I2S0 active for Bluetooth
    log_info "Checking AHUB I2S0 status (should be ON for Bluetooth):"
    tinymix -D 1 "I2S0OUT Switch"
    
    sleep 2
    
    # Test Bluetooth input
    log_info "Setting Bluetooth SCO input device..."
    setprop vendor.audio.test.input.routing "2048"  # AUDIO_DEVICE_IN_BLUETOOTH_SCO_HEADSET
    
    log_info "Checking AHUB I2S0 input status:"
    tinymix -D 1 "I2S0IN Switch"
    
    log_info "Bluetooth route test completed"
}

# Function to test Speaker routing
test_speaker_route() {
    log_info "=== Testing Speaker Route ==="
    
    # Simulate Android system setting Speaker output device
    log_info "Setting Speaker output device..."
    setprop vendor.audio.test.routing "2"  # AUDIO_DEVICE_OUT_SPEAKER
    
    # Check AHUB routing - should see I2S2 active for Speaker/TYW
    log_info "Checking AHUB I2S2 status (should be ON for Speaker):"
    tinymix -D 1 "I2S2OUT Switch"
    
    sleep 2
    
    # Test built-in mic input
    log_info "Setting Built-in Mic input device..."
    setprop vendor.audio.test.input.routing "1"  # AUDIO_DEVICE_IN_BUILTIN_MIC
    
    log_info "Checking AHUB I2S2 input status:"
    tinymix -D 1 "I2S2IN Switch"
    
    log_info "Speaker route test completed"
}

# Function to show current audio status
show_audio_status() {
    log_info "=== Current Audio Status ==="
    
    echo "Available audio cards:"
    cat /proc/asound/cards
    
    echo ""
    echo "AHUB I2S Switch Status:"
    tinymix -D 1 | grep -E "I2S[0-9]+(IN|OUT) Switch"
    
    echo ""
    echo "AHUB Source Selection:"
    tinymix -D 1 | grep -E "Src Select"
}

# Function to manually switch routes using tinymix
manual_bluetooth_switch() {
    log_info "=== Manual Bluetooth Switch ==="
    
    # Disable other I2S interfaces
    tinymix -D 1 "I2S1IN Switch" 0
    tinymix -D 1 "I2S1OUT Switch" 0
    tinymix -D 1 "I2S2IN Switch" 0
    tinymix -D 1 "I2S2OUT Switch" 0
    tinymix -D 1 "I2S3IN Switch" 0
    tinymix -D 1 "I2S3OUT Switch" 0
    
    # Enable I2S0 for Bluetooth
    tinymix -D 1 "I2S0IN Switch" 1
    tinymix -D 1 "I2S0OUT Switch" 1
    
    # Configure routing
    tinymix -D 1 "APBIF0 Src Select" "I2S0_TXDIF"
    tinymix -D 1 "I2S0 Src Select" "APBIF_TXDIF0"
    
    log_info "Manual Bluetooth switch completed"
}

manual_speaker_switch() {
    log_info "=== Manual Speaker Switch ==="
    
    # Disable other I2S interfaces
    tinymix -D 1 "I2S0IN Switch" 0
    tinymix -D 1 "I2S0OUT Switch" 0
    tinymix -D 1 "I2S1IN Switch" 0
    tinymix -D 1 "I2S1OUT Switch" 0
    tinymix -D 1 "I2S3IN Switch" 0
    tinymix -D 1 "I2S3OUT Switch" 0
    
    # Enable I2S2 for Speaker/TYW
    tinymix -D 1 "I2S2IN Switch" 1
    tinymix -D 1 "I2S2OUT Switch" 1
    
    # Configure routing
    tinymix -D 1 "APBIF2 Src Select" "I2S2_TXDIF"
    tinymix -D 1 "I2S2 Src Select" "APBIF_TXDIF2"
    
    log_info "Manual Speaker switch completed"
}

# Function to test audio playback
test_playback() {
    local mode="$1"
    
    log_info "=== Testing $mode Playback ==="
    
    if [ "$mode" = "bluetooth" ]; then
        manual_bluetooth_switch
        sleep 1
        if command -v tinyplay_ahub > /dev/null; then
            log_info "Playing test audio through Bluetooth..."
            timeout 5 tinyplay_ahub /system/media/audio/ringtones/Ring_Synth_04.ogg 2>/dev/null || true
        fi
    else
        manual_speaker_switch
        sleep 1
        if command -v tinyplay > /dev/null; then
            log_info "Playing test audio through Speaker..."
            timeout 5 tinyplay /system/media/audio/ringtones/Ring_Synth_04.ogg 2>/dev/null || true
        fi
    fi
    
    log_info "$mode playback test completed"
}

# Main function
case "$1" in
    "bluetooth"|"bt")
        test_bluetooth_route
        ;;
    "speaker"|"spk")
        test_speaker_route
        ;;
    "status")
        show_audio_status
        ;;
    "manual_bt")
        manual_bluetooth_switch
        ;;
    "manual_spk")
        manual_speaker_switch
        ;;
    "play_bt")
        test_playback "bluetooth"
        ;;
    "play_spk")
        test_playback "speaker"
        ;;
    "test")
        log_info "Running complete audio switching test..."
        show_audio_status
        echo ""
        test_speaker_route
        sleep 3
        test_bluetooth_route
        sleep 3
        test_speaker_route
        log_info "Complete test finished"
        ;;
    *)
        echo "Simple Audio Route Test Script"
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  bluetooth    - Test Bluetooth route via properties"
        echo "  speaker      - Test Speaker route via properties"
        echo "  manual_bt    - Manually switch to Bluetooth using tinymix"
        echo "  manual_spk   - Manually switch to Speaker using tinymix"
        echo "  play_bt      - Test Bluetooth playback"
        echo "  play_spk     - Test Speaker playback"
        echo "  status       - Show current audio status"
        echo "  test         - Run complete switching test"
        echo ""
        echo "Examples:"
        echo "  $0 manual_bt     # Switch to Bluetooth manually"
        echo "  $0 manual_spk    # Switch to Speaker manually"
        echo "  $0 status        # Check current status"
        ;;
esac
