#!/system/bin/sh

# Simple Audio Route Testing Script for T507
# Tests the basic audio device switching functionality

LOG_TAG="SimpleAudioTest"

log_info() {
    echo "[$LOG_TAG] $1"
    log -p i -t $LOG_TAG "$1"
}

# Function to test dynamic Bluetooth routing
test_bluetooth_route() {
    log_info "=== Testing Dynamic Bluetooth Route ==="

    # Test dynamic switching using AudioManager parameters
    log_info "Triggering dynamic Bluetooth output switch..."

    # Use tinymix to simulate AudioManager parameter setting
    # This should trigger out_set_parameters with AUDIO_DEVICE_OUT_BLUETOOTH_SCO_HEADSET
    if command -v am > /dev/null; then
        # Try using activity manager to trigger audio routing
        am broadcast -a android.intent.action.HEADSET_PLUG --ei state 1 --ei microphone 1 --es name "BT_SCO"
    fi

    # Alternative: directly test the HAL parameter interface
    log_info "Testing HAL parameter interface for Bluetooth..."

    # Check AHUB routing - should see I2S0 active for Bluetooth
    log_info "Checking AHUB I2S0 status (should be ON for Bluetooth):"
    tinymix -D 1 "I2S0OUT Switch"
    tinymix -D 1 "I2S0IN Switch"

    log_info "Dynamic Bluetooth route test completed"
}

# Function to test Speaker routing
test_speaker_route() {
    log_info "=== Testing Speaker Route ==="
    
    # Simulate Android system setting Speaker output device
    log_info "Setting Speaker output device..."
    setprop vendor.audio.test.routing "2"  # AUDIO_DEVICE_OUT_SPEAKER
    
    # Check AHUB routing - should see I2S2 active for Speaker/TYW
    log_info "Checking AHUB I2S2 status (should be ON for Speaker):"
    tinymix -D 1 "I2S2OUT Switch"
    
    sleep 2
    
    # Test built-in mic input
    log_info "Setting Built-in Mic input device..."
    setprop vendor.audio.test.input.routing "1"  # AUDIO_DEVICE_IN_BUILTIN_MIC
    
    log_info "Checking AHUB I2S2 input status:"
    tinymix -D 1 "I2S2IN Switch"
    
    log_info "Speaker route test completed"
}

# Function to show current audio status
show_audio_status() {
    log_info "=== Current Audio Status ==="
    
    echo "Available audio cards:"
    cat /proc/asound/cards
    
    echo ""
    echo "AHUB I2S Switch Status:"
    tinymix -D 1 | grep -E "I2S[0-9]+(IN|OUT) Switch"
    
    echo ""
    echo "AHUB Source Selection:"
    tinymix -D 1 | grep -E "Src Select"
}

# Function to manually switch routes using tinymix
manual_bluetooth_switch() {
    log_info "=== Manual Bluetooth Switch ==="
    
    # Disable other I2S interfaces
    tinymix -D 1 "I2S1IN Switch" 0
    tinymix -D 1 "I2S1OUT Switch" 0
    tinymix -D 1 "I2S2IN Switch" 0
    tinymix -D 1 "I2S2OUT Switch" 0
    tinymix -D 1 "I2S3IN Switch" 0
    tinymix -D 1 "I2S3OUT Switch" 0
    
    # Enable I2S0 for Bluetooth
    tinymix -D 1 "I2S0IN Switch" 1
    tinymix -D 1 "I2S0OUT Switch" 1
    
    # Configure routing
    tinymix -D 1 "APBIF0 Src Select" "I2S0_TXDIF"
    tinymix -D 1 "I2S0 Src Select" "APBIF_TXDIF0"
    
    log_info "Manual Bluetooth switch completed"
}

manual_speaker_switch() {
    log_info "=== Manual Speaker Switch ==="
    
    # Disable other I2S interfaces
    tinymix -D 1 "I2S0IN Switch" 0
    tinymix -D 1 "I2S0OUT Switch" 0
    tinymix -D 1 "I2S1IN Switch" 0
    tinymix -D 1 "I2S1OUT Switch" 0
    tinymix -D 1 "I2S3IN Switch" 0
    tinymix -D 1 "I2S3OUT Switch" 0
    
    # Enable I2S2 for Speaker/TYW
    tinymix -D 1 "I2S2IN Switch" 1
    tinymix -D 1 "I2S2OUT Switch" 1
    
    # Configure routing
    tinymix -D 1 "APBIF2 Src Select" "I2S2_TXDIF"
    tinymix -D 1 "I2S2 Src Select" "APBIF_TXDIF2"
    
    log_info "Manual Speaker switch completed"
}

# Function to test audio playback
test_playback() {
    local mode="$1"
    
    log_info "=== Testing $mode Playback ==="
    
    if [ "$mode" = "bluetooth" ]; then
        manual_bluetooth_switch
        sleep 1
        if command -v tinyplay_ahub > /dev/null; then
            log_info "Playing test audio through Bluetooth..."
            timeout 5 tinyplay_ahub /system/media/audio/ringtones/Ring_Synth_04.ogg 2>/dev/null || true
        fi
    else
        manual_speaker_switch
        sleep 1
        if command -v tinyplay > /dev/null; then
            log_info "Playing test audio through Speaker..."
            timeout 5 tinyplay /system/media/audio/ringtones/Ring_Synth_04.ogg 2>/dev/null || true
        fi
    fi
    
    log_info "$mode playback test completed"
}

# Function to test dynamic switching during playback
test_dynamic_switching() {
    log_info "=== Testing Dynamic Audio Switching ==="

    # Start with speaker mode
    log_info "Starting with Speaker mode..."
    manual_speaker_switch

    # Start a background audio playback if possible
    if [ -f "/system/media/audio/ringtones/Ring_Synth_04.ogg" ]; then
        log_info "Starting background audio playback..."
        tinyplay /system/media/audio/ringtones/Ring_Synth_04.ogg &
        PLAY_PID=$!
        sleep 2

        # Switch to Bluetooth during playback
        log_info "Switching to Bluetooth during playback..."
        manual_bluetooth_switch
        sleep 3

        # Switch back to Speaker during playback
        log_info "Switching back to Speaker during playback..."
        manual_speaker_switch
        sleep 3

        # Stop playback
        if [ ! -z "$PLAY_PID" ]; then
            kill $PLAY_PID 2>/dev/null || true
        fi

        log_info "Dynamic switching during playback test completed"
    else
        log_info "No test audio file found, testing routing only..."

        # Test routing switches without audio
        log_info "Testing rapid routing switches..."
        for i in 1 2 3; do
            log_info "Switch cycle $i..."
            manual_bluetooth_switch
            sleep 1
            manual_speaker_switch
            sleep 1
        done

        log_info "Rapid switching test completed"
    fi
}

# Main function
case "$1" in
    "bluetooth"|"bt")
        test_bluetooth_route
        ;;
    "speaker"|"spk")
        test_speaker_route
        ;;
    "status")
        show_audio_status
        ;;
    "manual_bt")
        manual_bluetooth_switch
        ;;
    "manual_spk")
        manual_speaker_switch
        ;;
    "play_bt")
        test_playback "bluetooth"
        ;;
    "play_spk")
        test_playback "speaker"
        ;;
    "dynamic")
        test_dynamic_switching
        ;;
    "test")
        log_info "Running complete audio switching test..."
        show_audio_status
        echo ""
        test_speaker_route
        sleep 3
        test_bluetooth_route
        sleep 3
        test_dynamic_switching
        log_info "Complete test finished"
        ;;
    *)
        echo "Simple Audio Route Test Script"
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  bluetooth    - Test Bluetooth route via properties"
        echo "  speaker      - Test Speaker route via properties"
        echo "  manual_bt    - Manually switch to Bluetooth using tinymix"
        echo "  manual_spk   - Manually switch to Speaker using tinymix"
        echo "  play_bt      - Test Bluetooth playback"
        echo "  play_spk     - Test Speaker playback"
        echo "  dynamic      - Test dynamic switching during playback"
        echo "  status       - Show current audio status"
        echo "  test         - Run complete switching test"
        echo ""
        echo "Examples:"
        echo "  $0 manual_bt     # Switch to Bluetooth manually"
        echo "  $0 manual_spk    # Switch to Speaker manually"
        echo "  $0 status        # Check current status"
        ;;
esac
