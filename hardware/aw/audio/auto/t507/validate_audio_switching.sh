#!/system/bin/sh

# T507 Audio Device Switching Validation Script
# This script validates the complete audio switching solution

LOG_TAG="AudioValidation"

log_info() {
    echo "[$LOG_TAG] $1"
    log -p i -t $LOG_TAG "$1"
}

log_error() {
    echo "[$LOG_TAG] ERROR: $1"
    log -p e -t $LOG_TAG "ERROR: $1"
}

log_success() {
    echo "[$LOG_TAG] SUCCESS: $1"
    log -p i -t $LOG_TAG "SUCCESS: $1"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    log_info "Running test: $test_name"
    
    if eval "$test_command"; then
        log_success "$test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "$test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test 1: Check audio cards availability
test_audio_cards() {
    log_info "=== Testing Audio Cards Availability ==="
    
    # Check if all required audio cards are present
    if ! cat /proc/asound/cards | grep -q "audiocodec"; then
        log_error "audiocodec card not found"
        return 1
    fi
    
    if ! cat /proc/asound/cards | grep -q "sndahub"; then
        log_error "sndahub card not found"
        return 1
    fi
    
    if ! cat /proc/asound/cards | grep -q "sndes721010043"; then
        log_error "sndes721010043 (TYW) card not found"
        return 1
    fi
    
    if ! cat /proc/asound/cards | grep -q "sndbt936b"; then
        log_error "sndbt936b (Bluetooth) card not found"
        return 1
    fi
    
    log_success "All required audio cards are present"
    return 0
}

# Test 2: Check AHUB mixer controls
test_ahub_controls() {
    log_info "=== Testing AHUB Mixer Controls ==="
    
    # Check if tinymix can access AHUB
    if ! tinymix -D 1 > /dev/null 2>&1; then
        log_error "Cannot access AHUB mixer controls"
        return 1
    fi
    
    # Check for required controls
    local required_controls=(
        "I2S0IN Switch"
        "I2S0OUT Switch"
        "I2S2IN Switch"
        "I2S2OUT Switch"
        "APBIF0 Src Select"
        "APBIF2 Src Select"
        "I2S0 Src Select"
        "I2S2 Src Select"
    )
    
    for control in "${required_controls[@]}"; do
        if ! tinymix -D 1 | grep -q "$control"; then
            log_error "Required control '$control' not found"
            return 1
        fi
    done
    
    log_success "All required AHUB controls are available"
    return 0
}

# Test 3: Test audio HAL loading
test_hal_loading() {
    log_info "=== Testing Audio HAL Loading ==="
    
    # Check if audio HAL library exists
    if [ ! -f "/vendor/lib/hw/audio.primary.t507.so" ] && [ ! -f "/system/lib/hw/audio.primary.t507.so" ]; then
        log_error "Audio HAL library not found"
        return 1
    fi
    
    # Check if AudioFlinger can load the HAL
    if ! getprop | grep -q "audio.primary"; then
        log_error "Audio HAL not loaded by AudioFlinger"
        return 1
    fi
    
    log_success "Audio HAL loaded successfully"
    return 0
}

# Test 4: Test speaker mode switching
test_speaker_mode() {
    log_info "=== Testing Speaker Mode Switching ==="
    
    # Switch to speaker mode using our script
    if [ -f "./audio_switch.sh" ]; then
        ./audio_switch.sh speaker > /dev/null 2>&1
    else
        # Fallback to direct tinymix commands
        tinymix -D 1 "I2S0IN Switch" 0
        tinymix -D 1 "I2S0OUT Switch" 0
        tinymix -D 1 "I2S2IN Switch" 1
        tinymix -D 1 "I2S2OUT Switch" 1
        tinymix -D 1 "APBIF2 Src Select" "I2S2_TXDIF"
        tinymix -D 1 "I2S2 Src Select" "APBIF_TXDIF2"
    fi
    
    sleep 2
    
    # Verify I2S2 is active
    if ! tinymix -D 1 "I2S2IN Switch" | grep -q "On"; then
        log_error "I2S2 input not activated"
        return 1
    fi
    
    if ! tinymix -D 1 "I2S2OUT Switch" | grep -q "On"; then
        log_error "I2S2 output not activated"
        return 1
    fi
    
    log_success "Speaker mode switching works"
    return 0
}

# Test 5: Test Bluetooth mode switching
test_bluetooth_mode() {
    log_info "=== Testing Bluetooth Mode Switching ==="
    
    # Switch to Bluetooth mode using our script
    if [ -f "./audio_switch.sh" ]; then
        ./audio_switch.sh bluetooth > /dev/null 2>&1
    else
        # Fallback to direct tinymix commands
        tinymix -D 1 "I2S2IN Switch" 0
        tinymix -D 1 "I2S2OUT Switch" 0
        tinymix -D 1 "I2S0IN Switch" 1
        tinymix -D 1 "I2S0OUT Switch" 1
        tinymix -D 1 "APBIF0 Src Select" "I2S0_TXDIF"
        tinymix -D 1 "I2S0 Src Select" "APBIF_TXDIF0"
    fi
    
    sleep 2
    
    # Verify I2S0 is active
    if ! tinymix -D 1 "I2S0IN Switch" | grep -q "On"; then
        log_error "I2S0 input not activated"
        return 1
    fi
    
    if ! tinymix -D 1 "I2S0OUT Switch" | grep -q "On"; then
        log_error "I2S0 output not activated"
        return 1
    fi
    
    log_success "Bluetooth mode switching works"
    return 0
}

# Test 6: Test audio playback capability
test_audio_playback() {
    log_info "=== Testing Audio Playback Capability ==="
    
    # Test speaker playback
    log_info "Testing speaker playback..."
    ./audio_switch.sh speaker > /dev/null 2>&1
    sleep 1
    
    # Try to play a short tone (if available)
    if command -v tinyplay > /dev/null; then
        # Generate a short test tone if possible
        if [ -f "/system/media/audio/ringtones/Ring_Synth_04.ogg" ]; then
            timeout 3 tinyplay /system/media/audio/ringtones/Ring_Synth_04.ogg > /dev/null 2>&1
            if [ $? -eq 0 ] || [ $? -eq 124 ]; then  # 124 is timeout exit code
                log_success "Speaker playback test passed"
            else
                log_error "Speaker playback failed"
                return 1
            fi
        fi
    fi
    
    # Test Bluetooth playback
    log_info "Testing Bluetooth playback..."
    ./audio_switch.sh bluetooth > /dev/null 2>&1
    sleep 1
    
    if command -v tinyplay_ahub > /dev/null; then
        if [ -f "/system/media/audio/ringtones/Ring_Synth_04.ogg" ]; then
            timeout 3 tinyplay_ahub /system/media/audio/ringtones/Ring_Synth_04.ogg > /dev/null 2>&1
            if [ $? -eq 0 ] || [ $? -eq 124 ]; then
                log_success "Bluetooth playback test passed"
            else
                log_error "Bluetooth playback failed"
                return 1
            fi
        fi
    fi
    
    return 0
}

# Test 7: Test parameter-based switching
test_parameter_switching() {
    log_info "=== Testing Parameter-based Switching ==="
    
    # Test setprop method
    setprop vendor.audio.mode.switch 1
    sleep 2
    
    # Verify speaker mode is active
    if ! tinymix -D 1 "I2S2OUT Switch" | grep -q "On"; then
        log_error "Parameter switching to speaker mode failed"
        return 1
    fi
    
    setprop vendor.audio.mode.switch 0
    sleep 2
    
    # Verify Bluetooth mode is active
    if ! tinymix -D 1 "I2S0OUT Switch" | grep -q "On"; then
        log_error "Parameter switching to Bluetooth mode failed"
        return 1
    fi
    
    log_success "Parameter-based switching works"
    return 0
}

# Main validation function
main() {
    log_info "=========================================="
    log_info "T507 Audio Device Switching Validation"
    log_info "=========================================="
    
    # Run all tests
    run_test "Audio Cards Availability" "test_audio_cards"
    run_test "AHUB Mixer Controls" "test_ahub_controls"
    run_test "Audio HAL Loading" "test_hal_loading"
    run_test "Speaker Mode Switching" "test_speaker_mode"
    run_test "Bluetooth Mode Switching" "test_bluetooth_mode"
    run_test "Audio Playback Capability" "test_audio_playback"
    run_test "Parameter-based Switching" "test_parameter_switching"
    
    # Print results
    log_info "=========================================="
    log_info "Validation Results:"
    log_info "Tests Passed: $TESTS_PASSED"
    log_info "Tests Failed: $TESTS_FAILED"
    log_info "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "ALL TESTS PASSED - Audio switching solution is working correctly!"
        return 0
    else
        log_error "SOME TESTS FAILED - Please check the implementation"
        return 1
    fi
}

# Run main function
main "$@"
